'use client'

import React, { memo, useCallback } from 'react'
import { ChevronUp } from 'lucide-react'

interface ScrollToTopProps {
  show: boolean
}

const ScrollToTop = memo(({ show }: ScrollToTopProps) => {
  const scrollToTop = useCallback(() => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }, [])

  if (!show) return null

  return (
    <button
      onClick={scrollToTop}
      className="fixed right-8 top-1/2 transform -translate-y-1/2 z-50 group"
      aria-label="Volver arriba"
    >
      <div className="relative">
        {/* Círculo exterior con doble borde */}
        <div className="w-16 h-16 rounded-full border-6 border-purple-600 bg-transparent flex items-center justify-center animate-pulse shadow-2xl">
          {/* Círculo interior */}
          <div className="w-12 h-12 rounded-full border-4 border-purple-500 bg-transparent flex items-center justify-center">
            <ChevronUp className="w-8 h-8 text-purple-600 font-bold" />
          </div>
        </div>
        {/* Efecto de brillo al hover */}
        <div className="absolute inset-0 rounded-full bg-purple-400/30 opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-pulse"></div>
      </div>
    </button>
  )
})

ScrollToTop.displayName = 'ScrollToTop'

export default ScrollToTop