'use client'

import React, { memo } from 'react'
import NavigationModals from './NavigationModals'
import MobileUserMenu from './MobileUserMenu'

interface NavigationBarProps {
  showCategorias: boolean
  showArriendos: boolean
  showServicios: boolean
  showMobileMenu: boolean
  onToggleCategorias: () => void
  onToggleArriendos: () => void
  onToggleServicios: () => void
  onToggleMobileMenu: () => void
}

const UserAuthLinks = memo(() => (
  <div className="flex items-center space-x-4">
    <div className="flex items-center space-x-2 text-white hover:text-yellow-200 transition-colors cursor-pointer text-sm font-medium">
      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
      </svg>
      <span>Registrarse</span>
    </div>
    <div className="flex items-center space-x-2 text-white hover:text-yellow-200 transition-colors cursor-pointer text-sm font-medium">
      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
      </svg>
      <span>Ingresar</span>
    </div>
  </div>
))

UserAuthLinks.displayName = 'UserAuthLinks'

const NavigationBar = memo(({
  showCategorias,
  showArriendos,
  showServicios,
  showMobileMenu,
  onToggleCategorias,
  onToggleArriendos,
  onToggleServicios,
  onToggleMobileMenu
}: NavigationBarProps) => (
  <nav className="bg-gradient-to-r from-purple-900 via-purple-800 to-purple-700 border-t border-yellow-400 shadow-md mb-4">
    {/* Versión Desktop */}
    <div className="hidden sm:block">
      <div className="container mx-auto px-6">
        <div className="flex items-center justify-between py-1">
          {/* Enlaces centrados: Categorías, Arriendos, Servicios */}
          <div className="flex-1 flex items-center justify-center space-x-8 relative">
            <NavigationModals
              showCategorias={showCategorias}
              showArriendos={showArriendos}
              showServicios={showServicios}
              onToggleCategorias={onToggleCategorias}
              onToggleArriendos={onToggleArriendos}
              onToggleServicios={onToggleServicios}
            />
          </div>
          
          {/* Enlaces al lado derecho: Registrarse, Ingresar con iconos */}
          <UserAuthLinks />
        </div>
      </div>
    </div>

    {/* Versión Móvil */}
    <div className="sm:hidden w-full">
      <div className="flex items-center justify-between px-4 py-1">
        {/* Enlaces a la izquierda: Categorías, Arriendos, Servicios - nombres completos */}
        <div className="flex items-center space-x-2">
          <NavigationModals
            showCategorias={showCategorias}
            showArriendos={showArriendos}
            showServicios={showServicios}
            onToggleCategorias={onToggleCategorias}
            onToggleArriendos={onToggleArriendos}
            onToggleServicios={onToggleServicios}
            isMobile={true}
          />
        </div>
        
        {/* Icono de hamburguesa a la derecha */}
        <MobileUserMenu
          showMobileMenu={showMobileMenu}
          onToggleMobileMenu={onToggleMobileMenu}
        />
      </div>
    </div>
  </nav>
))

NavigationBar.displayName = 'NavigationBar'

export default NavigationBar