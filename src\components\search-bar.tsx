'use client'

import { useState } from 'react'
import * as React from 'react'
import { Search, X } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'

interface SearchBarProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  className?: string
}

export function SearchBar({ value, onChange, placeholder = "Buscar...", className }: SearchBarProps) {
  const [isFocused, setIsFocused] = useState(false)

  const handleClear = () => {
    onChange('')
  }

  const handleSearch = () => {
    console.log('Buscando:', value)
    // Aquí iría la lógica de búsqueda
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      // Trigger search immediately on Enter
      handleSearch()
      e.currentTarget.blur()
    }
  }

  return (
    <div className={`relative max-w-2xl mx-auto transition-all duration-300 ${isFocused ? 'scale-105' : ''} ${className}`}>
      <div className="relative">
        <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white h-6 w-6 z-10" />
        <Input
          type="text"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          className={`pl-12 pr-24 py-2 text-lg text-white placeholder:text-white/70 border-2 transition-all duration-300 ${
            isFocused 
              ? 'border-white shadow-lg shadow-white/20' 
              : 'border-white/50 hover:border-white/80'
          } bg-white/10 backdrop-blur-sm`}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          onKeyPress={handleKeyPress}
        />
        
        {/* Botón de búsqueda */}
        <Button
          onClick={handleSearch}
          className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-yellow-400 hover:bg-yellow-500 text-purple-900 font-medium py-1 px-4 rounded-full transition-all duration-300 hover:scale-105 shadow-md z-20"
        >
          Buscar
        </Button>
        
        {/* Botón de limpiar (solo visible cuando hay texto) */}
        {value && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClear}
            className="absolute right-24 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 hover:bg-white/20 z-10"
          >
            <X className="h-4 w-4 text-white" />
          </Button>
        )}
      </div>
      
      {/* Decorative elements */}
      <div className="absolute -inset-1 bg-gradient-to-r from-white/20 to-white/10 rounded-lg blur opacity-30 group-hover:opacity-50 transition-opacity"></div>
    </div>
  )
}