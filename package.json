{"name": "nextjs_tailwind_shadcn_ts", "version": "0.1.0", "private": true, "scripts": {"dev": "nodemon --exec \"npx tsx server.ts\" --watch server.ts --watch src --ext ts,tsx,js,jsx", "build": "next build", "start": "set NODE_ENV=production && tsx server.ts", "lint": "next lint", "db:push": "prisma db push", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:reset": "prisma migrate reset"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.1.1", "@mdxeditor/editor": "^3.39.1", "@prisma/client": "^6.11.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@reactuses/core": "^6.0.5", "@tanstack/react-query": "^5.82.0", "@tanstack/react-table": "^8.21.3", "autoprefixer": "^10.4.21", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.23.2", "input-otp": "^1.4.2", "lightningcss": "^1.30.1", "lucide-react": "^0.525.0", "next": "15.3.5", "next-auth": "^4.24.11", "next-intl": "^4.3.4", "next-themes": "^0.4.6", "postcss": "^8.5.6", "prisma": "^6.11.1", "react": "^19.0.0", "react-day-picker": "^9.8.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^3.0.3", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.15.4", "sharp": "^0.34.3", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tailwindcss-animate": "^1.0.7", "tsx": "^4.20.3", "uuid": "^11.1.0", "vaul": "^1.1.2", "z-ai-web-dev-sdk": "^0.0.10", "zod": "^4.0.2", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@mapbox/node-pre-gyp": "^2.0.0", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "nodemon": "^3.1.10", "typescript": "^5"}}