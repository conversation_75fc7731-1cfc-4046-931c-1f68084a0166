'use client'

import { useState, useEffect } from 'react'
import { ProductCard } from '@/components/product-card-simple'
import { SearchBar } from '@/components/search-bar'
import { CardCarousel } from '@/components/card-carousel'
import { BannerCarousel } from '@/components/banner-carousel'
import { ImageCarouselContinuous } from '@/components/image-carousel-continuous'
import { ImageCarouselContinuous2 } from '@/components/image-carousel-continuous2'
import { InfoBannerCarousel } from '@/components/info-banner-carousel'
import { Product } from '@/types/product'
import { ChevronDown, Mail, Phone, MessageCircle, Users, Store, HelpCircle, Shield, Cookie, RefreshCw, FileText, ChevronUp, Menu } from 'lucide-react'

export default function Home() {
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('todos')
  
  // Estados para los modales desplegables
  const [showCategorias, setShowCategorias] = useState(false)
  const [showArriendos, setShowArriendos] = useState(false)
  const [showServicios, setShowServicios] = useState(false)
  
  // Estado para el menú móvil
  const [showMobileMenu, setShowMobileMenu] = useState(false)
  
  // Estado para controlar la visibilidad de la flecha de volver arriba
  const [showScrollToTop, setShowScrollToTop] = useState(false)

  // Función para cerrar todos los modales
  const closeAllModals = () => {
    setShowCategorias(false)
    setShowArriendos(false)
    setShowServicios(false)
    setShowMobileMenu(false)
  }

  // Función para toggle de categorías (cierra otros modales)
  const toggleCategorias = () => {
    if (showCategorias) {
      setShowCategorias(false)
    } else {
      closeAllModals()
      setShowCategorias(true)
    }
  }

  // Función para toggle de arriendos (cierra otros modales)
  const toggleArriendos = () => {
    if (showArriendos) {
      setShowArriendos(false)
    } else {
      closeAllModals()
      setShowArriendos(true)
    }
  }

  // Función para toggle de servicios (cierra otros modales)
  const toggleServicios = () => {
    if (showServicios) {
      setShowServicios(false)
    } else {
      closeAllModals()
      setShowServicios(true)
    }
  }

  // Función para toggle del menú móvil (cierra otros modales)
  const toggleMobileMenu = () => {
    if (showMobileMenu) {
      setShowMobileMenu(false)
    } else {
      closeAllModals()
      setShowMobileMenu(true)
    }
  }

  useEffect(() => {
    fetchProducts()
  }, [])

  // Efecto para cerrar modales al hacer clic fuera
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Si algún modal está abierto y el clic fue fuera de los modales
      if ((showCategorias || showArriendos || showServicios || showMobileMenu) && 
          !(event.target as Element).closest('.modal-container')) {
        closeAllModals()
      }
    }

    // Agregar event listener
    document.addEventListener('mousedown', handleClickOutside)
    
    // Limpiar event listener
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showCategorias, showArriendos, showServicios, showMobileMenu])

  // Efecto para detectar scroll y mostrar/ocultar flecha de volver arriba
  useEffect(() => {
    const handleScroll = () => {
      // Obtener la posición del header
      const header = document.querySelector('header')
      if (header) {
        const headerRect = header.getBoundingClientRect()
        // Mostrar flecha si el header no está visible (ha salido de la pantalla)
        setShowScrollToTop(headerRect.bottom < 0)
      }
    }

    // Agregar event listener de scroll
    window.addEventListener('scroll', handleScroll)
    
    // Verificar estado inicial
    handleScroll()
    
    // Limpiar event listener
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [])

  const fetchProducts = async () => {
    try {
      const response = await fetch('/api/products')
      const data = await response.json()
      setProducts(data)
    } catch (error) {
      console.error('Error fetching products:', error)
    } finally {
      setLoading(false)
    }
  }

  // Función para volver al inicio de la página
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'todos' || product.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  return (
    <div className="min-h-screen bg-background">
      {/* Header Principal */}
      <header className="relative text-white shadow-2xl" style={{ background: 'linear-gradient(90deg, #3b0764 0%, #4c1d95 20%, #6d28d9 40%, var(--yellow-accent) 100%)' }}>
        <div className="container mx-auto relative z-10">
          {/* Versión Desktop */}
          <div className="hidden sm:block py-6 px-6">
            <div className="flex items-center justify-between">
              {/* Logo/Título - Dos filas a la izquierda */}
              <div className="flex flex-col items-start">
                <h1 className="text-xl font-bold leading-tight text-white">Solo a</h1>
                <h1 className="text-4xl font-bold leading-tight text-yellow-300">un CLICK</h1>
              </div>

              {/* Buscador en el centro */}
              <div className="flex-1 max-w-lg mx-8">
                <SearchBar 
                  value={searchTerm} 
                  onChange={setSearchTerm}
                  placeholder="Buscar productos..."
                />
              </div>

              {/* Espacio publicitario a la derecha */}
              <div className="group w-56 h-16 bg-gradient-to-br from-purple-700/80 via-purple-600/70 to-purple-500/60 border-2 border-purple-300/60 rounded-xl flex items-center justify-center p-3 hover:shadow-xl hover:shadow-purple-400/40 transition-all duration-500 hover:scale-105 backdrop-blur-sm relative overflow-hidden">
                {/* Efecto de brillo */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent transform -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
                
                {/* Icono decorativo */}
                <div className="absolute top-1 right-2">
                  <svg className="w-3 h-3 text-white/60" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8 2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                  </svg>
                </div>
                
                <div className="text-center relative z-10">
                  <div className="text-[9px] font-extrabold text-white leading-tight tracking-wider uppercase">Espacio Premium</div>
                  <div className="text-xs font-bold text-white leading-tight tracking-wide">Publicitario</div>
                  <div className="text-[9px] text-purple-200 leading-tight mt-0.5">• Disponible •</div>
                </div>
              </div>
            </div>
          </div>

          {/* Versión Móvil */}
          <div className="sm:hidden w-full">
            {/* Primera fila: Título y buscador */}
            <div className="flex items-center justify-between px-4 py-3">
              {/* Logo/Título - Más pequeño y a la izquierda */}
              <div className="flex flex-col items-start">
                <h1 className="text-sm font-bold leading-tight text-white">Solo a</h1>
                <h1 className="text-lg font-bold leading-tight text-yellow-300">un CLICK</h1>
              </div>

              {/* Buscador a la derecha - Más pequeño */}
              <div className="flex-1 max-w-[180px] ml-3">
                <SearchBar 
                  value={searchTerm} 
                  onChange={setSearchTerm}
                  placeholder="Buscar..."
                  className="text-sm"
                />
              </div>
            </div>

            {/* Segunda fila: Banner publicitario */}
            <div className="px-4 pb-3">
              <div className="group w-full h-12 bg-gradient-to-br from-purple-700/80 via-purple-600/70 to-purple-500/60 border-2 border-purple-300/60 rounded-lg flex items-center justify-center p-2 hover:shadow-lg hover:shadow-purple-400/40 transition-all duration-500 backdrop-blur-sm relative overflow-hidden">
                {/* Efecto de brillo */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent transform -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
                
                {/* Icono decorativo */}
                <div className="absolute top-1 right-2">
                  <svg className="w-2 h-2 text-white/60" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8 2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                  </svg>
                </div>
                
                <div className="text-center relative z-10">
                  <div className="text-[8px] font-extrabold text-white leading-tight tracking-wider uppercase">Espacio Premium</div>
                  <div className="text-[10px] font-bold text-white leading-tight tracking-wide">Publicitario</div>
                  <div className="text-[8px] text-purple-200 leading-tight mt-0.5">• Disponible •</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Barra de navegación inferior */}
      <nav className="bg-gradient-to-r from-purple-900 via-purple-800 to-purple-700 border-t border-yellow-400 shadow-md mb-4">
        {/* Versión Desktop */}
        <div className="hidden sm:block">
          <div className="container mx-auto px-6">
            <div className="flex items-center justify-between py-1">
              {/* Enlaces centrados: Categorías, Arriendos, Servicios */}
              <div className="flex-1 flex items-center justify-center space-x-8 relative">
                {/* Categorías */}
                <div className="relative modal-container">
                  <button 
                    onClick={toggleCategorias}
                    className="flex items-center space-x-2 text-white/90 hover:text-white transition-colors cursor-pointer text-sm font-medium"
                  >
                    <ChevronDown className={`w-4 h-4 transition-transform duration-200 ${showCategorias ? 'rotate-180' : ''}`} />
                    <span>Categorías</span>
                  </button>
                  
                  {/* Modal desplegable de Categorías */}
                  {showCategorias && (
                    <div className="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-xl border border-gray-200 z-50">
                      <div className="py-2">
                        <div className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer">Electrónica</div>
                        <div className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer">Ropa y Accesorios</div>
                        <div className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer">Hogar y Jardín</div>
                        <div className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer">Deportes</div>
                        <div className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer">Belleza y Salud</div>
                      </div>
                    </div>
                  )}
                </div>
                
                {/* Arriendos */}
                <div className="relative modal-container">
                  <button 
                    onClick={toggleArriendos}
                    className="flex items-center space-x-2 text-white/90 hover:text-white transition-colors cursor-pointer text-sm font-medium"
                  >
                    <ChevronDown className={`w-4 h-4 transition-transform duration-200 ${showArriendos ? 'rotate-180' : ''}`} />
                    <span>Arriendos</span>
                  </button>
                  
                  {/* Modal desplegable de Arriendos */}
                  {showArriendos && (
                    <div className="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-xl border border-gray-200 z-50">
                      <div className="py-2">
                        <div className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer">Departamentos</div>
                        <div className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer">Casas</div>
                        <div className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer">Oficinas</div>
                        <div className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer">Bodegas</div>
                        <div className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer">Estacionamientos</div>
                      </div>
                    </div>
                  )}
                </div>
                
                {/* Servicios */}
                <div className="relative modal-container">
                  <button 
                    onClick={toggleServicios}
                    className="flex items-center space-x-2 text-white/90 hover:text-white transition-colors cursor-pointer text-sm font-medium"
                  >
                    <ChevronDown className={`w-4 h-4 transition-transform duration-200 ${showServicios ? 'rotate-180' : ''}`} />
                    <span>Servicios</span>
                  </button>
                  
                  {/* Modal desplegable de Servicios */}
                  {showServicios && (
                    <div className="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-xl border border-gray-200 z-50">
                      <div className="py-2">
                        <div className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer">Servicios Profesionales</div>
                        <div className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer">Mantenimiento y Reparación</div>
                        <div className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer">Consultoría</div>
                        <div className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer">Educación y Cursos</div>
                        <div className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer">Salud y Bienestar</div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              
              {/* Enlaces al lado derecho: Registrarse, Ingresar con iconos */}
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2 text-white hover:text-yellow-200 transition-colors cursor-pointer text-sm font-medium">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                  </svg>
                  <span>Registrarse</span>
                </div>
                <div className="flex items-center space-x-2 text-white hover:text-yellow-200 transition-colors cursor-pointer text-sm font-medium">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                  </svg>
                  <span>Ingresar</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Versión Móvil */}
        <div className="sm:hidden w-full">
          <div className="flex items-center justify-between px-4 py-1">
            {/* Enlaces a la izquierda: Categorías, Arriendos, Servicios - nombres completos */}
            <div className="flex items-center space-x-2">
              {/* Categorías */}
              <div className="relative modal-container">
                <button 
                  onClick={toggleCategorias}
                  className="flex items-center space-x-1 text-white/90 hover:text-white transition-colors cursor-pointer text-[10px] font-medium"
                >
                  <ChevronDown className={`w-3 h-3 transition-transform duration-200 ${showCategorias ? 'rotate-180' : ''}`} />
                  <span>Categorías</span>
                </button>
                
                {/* Modal desplegable de Categorías */}
                {showCategorias && (
                  <div className="absolute top-full left-0 mt-1 w-40 bg-white rounded-lg shadow-xl border border-gray-200 z-50">
                    <div className="py-1">
                      <div className="px-3 py-1 text-xs text-gray-700 hover:bg-gray-100 cursor-pointer">Electrónica</div>
                      <div className="px-3 py-1 text-xs text-gray-700 hover:bg-gray-100 cursor-pointer">Ropa</div>
                      <div className="px-3 py-1 text-xs text-gray-700 hover:bg-gray-100 cursor-pointer">Hogar</div>
                      <div className="px-3 py-1 text-xs text-gray-700 hover:bg-gray-100 cursor-pointer">Deportes</div>
                      <div className="px-3 py-1 text-xs text-gray-700 hover:bg-gray-100 cursor-pointer">Belleza</div>
                    </div>
                  </div>
                )}
              </div>
              
              {/* Arriendos */}
              <div className="relative modal-container">
                <button 
                  onClick={toggleArriendos}
                  className="flex items-center space-x-1 text-white/90 hover:text-white transition-colors cursor-pointer text-[10px] font-medium"
                >
                  <ChevronDown className={`w-3 h-3 transition-transform duration-200 ${showArriendos ? 'rotate-180' : ''}`} />
                  <span>Arriendos</span>
                </button>
                
                {/* Modal desplegable de Arriendos */}
                {showArriendos && (
                  <div className="absolute top-full left-0 mt-1 w-40 bg-white rounded-lg shadow-xl border border-gray-200 z-50">
                    <div className="py-1">
                      <div className="px-3 py-1 text-xs text-gray-700 hover:bg-gray-100 cursor-pointer">Departamentos</div>
                      <div className="px-3 py-1 text-xs text-gray-700 hover:bg-gray-100 cursor-pointer">Casas</div>
                      <div className="px-3 py-1 text-xs text-gray-700 hover:bg-gray-100 cursor-pointer">Oficinas</div>
                      <div className="px-3 py-1 text-xs text-gray-700 hover:bg-gray-100 cursor-pointer">Bodegas</div>
                      <div className="px-3 py-1 text-xs text-gray-700 hover:bg-gray-100 cursor-pointer">Estaciona</div>
                    </div>
                  </div>
                )}
              </div>
              
              {/* Servicios */}
              <div className="relative modal-container">
                <button 
                  onClick={toggleServicios}
                  className="flex items-center space-x-1 text-white/90 hover:text-white transition-colors cursor-pointer text-[10px] font-medium"
                >
                  <ChevronDown className={`w-3 h-3 transition-transform duration-200 ${showServicios ? 'rotate-180' : ''}`} />
                  <span>Servicios</span>
                </button>
                
                {/* Modal desplegable de Servicios */}
                {showServicios && (
                  <div className="absolute top-full left-0 mt-1 w-40 bg-white rounded-lg shadow-xl border border-gray-200 z-50">
                    <div className="py-1">
                      <div className="px-3 py-1 text-xs text-gray-700 hover:bg-gray-100 cursor-pointer">Profesionales</div>
                      <div className="px-3 py-1 text-xs text-gray-700 hover:bg-gray-100 cursor-pointer">Mantenimiento</div>
                      <div className="px-3 py-1 text-xs text-gray-700 hover:bg-gray-100 cursor-pointer">Consultoría</div>
                      <div className="px-3 py-1 text-xs text-gray-700 hover:bg-gray-100 cursor-pointer">Educación</div>
                      <div className="px-3 py-1 text-xs text-gray-700 hover:bg-gray-100 cursor-pointer">Salud</div>
                    </div>
                  </div>
                )}
              </div>
            </div>
            
            {/* Icono de hamburguesa a la derecha */}
            <div className="relative modal-container">
              <button 
                onClick={toggleMobileMenu}
                className="flex items-center justify-center w-8 h-8 text-white/90 hover:text-white transition-colors cursor-pointer"
              >
                <Menu className="w-5 h-5" />
              </button>
              
              {/* Menú desplegable de opciones de usuario */}
              {showMobileMenu && (
                <div className="absolute top-full right-0 mt-1 w-40 bg-white rounded-lg shadow-xl border border-gray-200 z-50">
                  <div className="py-1">
                    <div className="flex items-center space-x-2 px-3 py-2 text-xs text-gray-700 hover:bg-gray-100 cursor-pointer">
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                      </svg>
                      <span>Registrarse</span>
                    </div>
                    <div className="flex items-center space-x-2 px-3 py-2 text-xs text-gray-700 hover:bg-gray-100 cursor-pointer">
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                      </svg>
                      <span>Ingresar</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </nav>

      {/* Carrusel Continuo de 12 Imágenes */}
      <ImageCarouselContinuous 
        images={[
          "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop",
          "https://images.unsplash.com/photo-1483985988355-7647b8b3e340?w=400&h=300&fit=crop",
          "https://images.unsplash.com/photo-1556740738-b6a63e27c4df?w=400&h=300&fit=crop",
          "https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400&h=300&fit=crop",
          "https://images.unsplash.com/photo-1491553895911-0055eca6402d?w=400&h=300&fit=crop",
          "https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=400&h=300&fit=crop",
          "https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=300&fit=crop",
          "https://images.unsplash.com/photo-1503376780353-7e6692767b70?w=400&h=300&fit=crop",
          "https://images.unsplash.com/photo-1551107696-a4b0c5a0b9b1?w=400&h=300&fit=crop",
          "https://images.unsplash.com/photo-1526170375885-4d8ec677e1c8?w=400&h=300&fit=crop",
          "https://images.unsplash.com/photo-1551024601-bec78aea804d?w=400&h=300&fit=crop",
          "https://images.unsplash.com/photo-1546868871-7041f2a55e12?w=400&h=300&fit=crop"
        ]}
        autoPlayInterval={6000}
      />

      {/* Main Content */}
      <main className="container mx-auto py-8 px-6">
        {/* Sección Destacados */}
        <CardCarousel
          title="Destacados"
          subtitle="Los productos más populares del momento"
          products={filteredProducts.slice(0, 10)}
          cardKeyPrefix="destacados"
        />

        {/* Sección Ofertas */}
        <CardCarousel
          title="Ofertas"
          subtitle="Descuentos exclusivos por tiempo limitado"
          products={filteredProducts.filter(p => p.discount && p.discount > 0).slice(0, 10)}
          cardKeyPrefix="ofertas"
        />

  

        {/* Sección Novedades */}
        <CardCarousel
          title="Novedades"
          subtitle="Los últimos lanzamientos del mercado"
          products={filteredProducts.slice(4, 14)}
          cardKeyPrefix="novedades"
        />

        {/* Sección Tendencias */}
        <CardCarousel
          title="Tendencias"
          subtitle="Lo más buscado y deseado actualmente"
          products={filteredProducts.slice(2, 12)}
          cardKeyPrefix="tendencias"
        />

        {/* Segundo Carrusel Continuo de 12 Imágenes */}
        <ImageCarouselContinuous2 
          images={[
            "https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=400&h=300&fit=crop",
            "https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=400&h=300&fit=crop",
            "https://images.unsplash.com/photo-1503376780353-7b66bfc32e44?w=400&h=300&fit=crop",
            "https://images.unsplash.com/photo-1556740738-b6a63e27c4df?w=400&h=300&fit=crop",
            "https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=300&fit=crop",
            "https://images.unsplash.com/photo-1490474418585-ba9bad8fd0ea?w=400&h=300&fit=crop",
            "https://images.unsplash.com/photo-1518770660439-4636190af475?w=400&h=300&fit=crop",
            "https://images.unsplash.com/photo-1550745165-9bc0b252726f?w=400&h=300&fit=crop",
            "https://images.unsplash.com/photo-1551818255-e6e10975bc51?w=400&h=300&fit=crop",
            "https://images.unsplash.com/photo-1526170375885-4d8ec677e1c8?w=400&h=300&fit=crop",
            "https://images.unsplash.com/photo-1551024601-bec78aea804d?w=400&h=300&fit=crop",
            "https://images.unsplash.com/photo-1546868871-7041f2a55e12?w=400&h=300&fit=crop"
          ]}
          autoPlayInterval={6000}
        />

        {/* Sección ¡No te lo Pierdas! */}
        <CardCarousel
          title="¡No te lo Pierdas!"
          subtitle="Oportunidades únicas que no puedes dejar pasar"
          products={filteredProducts.slice(6, 16)}
          cardKeyPrefix="no-te-lo-pierdas"
        />

        {/* Carrusel Informativo */}
        <InfoBannerCarousel autoPlayInterval={5000}>
          {/* Banner 1: Plataforma Premium para Empresas */}
          <section className="mb-8 md:mb-12">
            <div className="relative overflow-hidden rounded-3xl shadow-2xl h-[280px] md:h-[320px] bg-gradient-to-br from-slate-900 via-purple-900 to-slate-800">
              {/* Patrón de fondo profesional */}
              <div className="absolute inset-0 opacity-20">
                <div className="absolute inset-0" style={{
                  backgroundImage: `radial-gradient(circle at 20% 50%, rgba(168, 85, 247, 0.3) 0%, transparent 50%), 
                                   radial-gradient(circle at 80% 80%, rgba(59, 130, 246, 0.3) 0%, transparent 50%)`
                }}></div>
                {/* Líneas de cuadrícula sutil */}
                <div className="absolute inset-0" style={{
                  backgroundImage: `linear-gradient(rgba(255,255,255,0.05) 1px, transparent 1px), 
                                   linear-gradient(90deg, rgba(255,255,255,0.05) 1px, transparent 1px)`,
                  backgroundSize: '50px 50px'
                }}></div>
              </div>
              
              {/* Elementos decorativos flotantes */}
              <div className="absolute top-8 left-8 w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
              <div className="absolute bottom-12 right-12 w-3 h-3 bg-blue-400 rounded-full animate-ping"></div>
              <div className="absolute top-1/3 right-1/4 w-1 h-1 bg-white rounded-full animate-pulse"></div>
              
              {/* Contenido principal */}
              <div className="relative z-10 h-full flex flex-col lg:flex-row items-center justify-between p-8 md:p-12">
                {/* Lado izquierdo - Contenido */}
                <div className="flex-1 max-w-2xl text-center lg:text-left">
                  {/* Badge de categoría */}
                  <div className="inline-flex items-center gap-2 bg-gradient-to-r from-purple-600/20 to-blue-600/20 backdrop-blur-sm px-4 py-2 rounded-full mb-6 border border-purple-500/30">
                    <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                    <span className="text-purple-300 font-semibold text-sm tracking-wide">PLATAFORMA EMPRESARIAL</span>
                  </div>
                  
                  {/* Título principal */}
                  <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6 leading-tight">
                    Transforma tu Negocio
                    <span className="block bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent mt-2">
                      Alcanza Clientes Globales
                    </span>
                  </h2>
                  
                  {/* Subtítulo profesional */}
                  <p className="text-gray-300 text-lg md:text-xl mb-8 leading-relaxed max-w-lg">
                    La plataforma líder para empresas que buscan expansión digital. 
                    Conecta con miles de clientes potenciales diariamente.
                  </p>
                  
                  {/* Estadísticas impactantes */}
                  <div className="flex flex-wrap gap-6 mb-8 justify-center lg:justify-start">
                    <div className="text-center">
                      <div className="text-2xl md:text-3xl font-bold text-purple-400">10K+</div>
                      <div className="text-sm text-gray-400">Empresas Activas</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl md:text-3xl font-bold text-blue-400">50K+</div>
                      <div className="text-sm text-gray-400">Clientes Diarios</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl md:text-3xl font-bold text-green-400">98%</div>
                      <div className="text-sm text-gray-400">Satisfacción</div>
                    </div>
                  </div>
                  
                  {/* CTA Buttons */}
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <button className="group relative bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold px-8 py-4 rounded-xl transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-purple-500/40 overflow-hidden">
                      <span className="relative z-10 text-lg">Comenzar Ahora</span>
                      <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </button>
                    <button className="bg-white/10 hover:bg-white/20 text-white font-semibold px-8 py-4 rounded-xl transition-all duration-300 backdrop-blur-md border border-white/20 text-lg">
                      Ver Demo
                    </button>
                  </div>
                </div>
                
                {/* Lado derecho - Visual profesional */}
                <div className="flex-shrink-0 mt-8 lg:mt-0">
                  <div className="relative">
                    {/* Círculo principal con efecto neón */}
                    <div className="w-48 h-48 md:w-56 md:h-56 bg-gradient-to-br from-purple-600/20 to-blue-600/20 backdrop-blur-md rounded-full flex items-center justify-center border border-purple-500/30 relative overflow-hidden">
                      {/* Efecto de luz interior */}
                      <div className="absolute inset-0 bg-gradient-to-br from-purple-400/10 to-blue-400/10 animate-pulse"></div>
                      
                      {/* Icono central */}
                      <div className="relative z-10">
                        <svg className="w-20 h-20 md:w-24 md:h-24 text-purple-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                      </div>
                    </div>
                    
                    {/* Anillos concéntricos animados */}
                    <div className="absolute inset-0 border border-purple-400/20 rounded-full animate-ping"></div>
                    <div className="absolute inset-4 border border-blue-400/20 rounded-full animate-ping" style={{animationDelay: '1s'}}></div>
                    
                    {/* Partículas flotantes */}
                    <div className="absolute -top-4 -right-4 w-6 h-6 bg-purple-400 rounded-full animate-bounce"></div>
                    <div className="absolute -bottom-4 -left-4 w-4 h-4 bg-blue-400 rounded-full animate-bounce" style={{animationDelay: '0.5s'}}></div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Banner 2: Seguridad Empresarial de Nivel Enterprise */}
          <section className="mb-8 md:mb-12">
            <div className="relative overflow-hidden rounded-3xl shadow-2xl h-[280px] md:h-[320px] bg-gradient-to-br from-slate-900 via-emerald-900 to-slate-800">
              {/* Patrón de fondo de seguridad */}
              <div className="absolute inset-0 opacity-15">
                <div className="absolute inset-0" style={{
                  backgroundImage: `radial-gradient(circle at 30% 30%, rgba(16, 185, 129, 0.4) 0%, transparent 50%), 
                                   radial-gradient(circle at 70% 70%, rgba(6, 182, 212, 0.4) 0%, transparent 50%)`
                }}></div>
                {/* Patrón hexagonal de seguridad */}
                <div className="absolute inset-0 opacity-10" style={{
                  backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.3'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
                  backgroundSize: '60px 60px'
                }}></div>
              </div>
              
              {/* Líneas de conexión animadas */}
              <div className="absolute inset-0">
                <svg className="w-full h-full opacity-20">
                  <line x1="10%" y1="20%" x2="90%" y2="80%" stroke="url(#gradient1)" strokeWidth="1" strokeDasharray="5,5">
                    <animate attributeName="stroke-dashoffset" values="0;10" dur="20s" repeatCount="indefinite"/>
                  </line>
                  <line x1="20%" y1="80%" x2="80%" y2="20%" stroke="url(#gradient2)" strokeWidth="1" strokeDasharray="5,5">
                    <animate attributeName="stroke-dashoffset" values="0;10" dur="15s" repeatCount="indefinite"/>
                  </line>
                  <defs>
                    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" stopColor="#10b981" />
                      <stop offset="100%" stopColor="#06b6d4" />
                    </linearGradient>
                    <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" stopColor="#06b6d4" />
                      <stop offset="100%" stopColor="#10b981" />
                    </linearGradient>
                  </defs>
                </svg>
              </div>
              
              {/* Elementos de seguridad flotantes */}
              <div className="absolute top-6 left-6 w-3 h-3 bg-emerald-400 rounded-full animate-pulse"></div>
              <div className="absolute bottom-8 right-8 w-2 h-2 bg-cyan-400 rounded-full animate-ping"></div>
              <div className="absolute top-1/2 left-1/4 w-1 h-1 bg-green-400 rounded-full animate-pulse"></div>
              
              {/* Contenido principal */}
              <div className="relative z-10 h-full flex flex-col lg:flex-row items-center justify-between p-8 md:p-12">
                {/* Lado izquierdo - Contenido */}
                <div className="flex-1 max-w-2xl text-center lg:text-left">
                  {/* Badge de seguridad */}
                  <div className="inline-flex items-center gap-2 bg-gradient-to-r from-emerald-600/20 to-cyan-600/20 backdrop-blur-sm px-4 py-2 rounded-full mb-6 border border-emerald-500/30">
                    <svg className="w-4 h-4 text-emerald-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-emerald-300 font-semibold text-sm tracking-wide">SEGURIDAD ENTERPRISE</span>
                  </div>
                  
                  {/* Título principal */}
                  <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6 leading-tight">
                    Protección de Datos
                    <span className="block bg-gradient-to-r from-emerald-400 to-cyan-400 bg-clip-text text-transparent mt-2">
                      Garantizada y Certificada
                    </span>
                  </h2>
                  
                  {/* Subtítulo profesional */}
                  <p className="text-gray-300 text-lg md:text-xl mb-8 leading-relaxed max-w-lg">
                    Seguridad de nivel bancario con cifrado AES-256 y 
                    certificaciones internacionales de protección de datos.
                  </p>
                  
                  {/* Certificaciones y sellos */}
                  <div className="flex flex-wrap gap-4 mb-8 justify-center lg:justify-start">
                    <div className="bg-white/10 backdrop-blur-md px-4 py-3 rounded-xl border border-white/20">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-emerald-500/20 rounded-lg flex items-center justify-center">
                          <svg className="w-5 h-5 text-emerald-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <div>
                          <div className="text-emerald-400 font-semibold text-sm">SSL 256-bit</div>
                          <div class="text-gray-400 text-xs">Cifrado Máximo</div>
                        </div>
                      </div>
                    </div>
                    <div className="bg-white/10 backdrop-blur-md px-4 py-3 rounded-xl border border-white/20">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-cyan-500/20 rounded-lg flex items-center justify-center">
                          <svg className="w-5 h-5 text-cyan-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z" />
                          </svg>
                        </div>
                        <div>
                          <div className="text-cyan-400 font-semibold text-sm">GDPR Compliant</div>
                          <div class="text-gray-400 text-xs">Protección UE</div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* CTA Buttons */}
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <button className="group relative bg-gradient-to-r from-emerald-600 to-cyan-600 text-white font-semibold px-8 py-4 rounded-xl transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-emerald-500/40 overflow-hidden">
                      <span className="relative z-10 text-lg">Ver Certificados</span>
                      <div className="absolute inset-0 bg-gradient-to-r from-cyan-600 to-emerald-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </button>
                    <button className="bg-white/10 hover:bg-white/20 text-white font-semibold px-8 py-4 rounded-xl transition-all duration-300 backdrop-blur-md border border-white/20 text-lg">
                      Política de Seguridad
                    </button>
                  </div>
                </div>
                
                {/* Lado derecho - Visual de seguridad */}
                <div className="flex-shrink-0 mt-8 lg:mt-0">
                  <div className="relative">
                    {/* Escudo principal con efecto 3D */}
                    <div className="w-48 h-48 md:w-56 md:h-56 bg-gradient-to-br from-emerald-600/20 to-cyan-600/20 backdrop-blur-md rounded-full flex items-center justify-center border border-emerald-500/30 relative overflow-hidden transform hover:scale-105 transition-transform duration-300">
                      {/* Efecto de energía interior */}
                      <div className="absolute inset-0 bg-gradient-to-br from-emerald-400/10 to-cyan-400/10 animate-pulse"></div>
                      
                      {/* Icono de escudo */}
                      <div className="relative z-10">
                        <svg className="w-24 h-24 md:w-28 md:h-28 text-emerald-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                        </svg>
                      </div>
                    </div>
                    
                    {/* Anillos de protección animados */}
                    <div className="absolute inset-0 border border-emerald-400/20 rounded-full animate-ping"></div>
                    <div className="absolute inset-4 border border-cyan-400/20 rounded-full animate-ping" style={{animationDelay: '1s'}}></div>
                    <div className="absolute inset-8 border border-green-400/20 rounded-full animate-ping" style={{animationDelay: '2s'}}></div>
                    
                    {/* Partículas de seguridad */}
                    <div className="absolute -top-2 -right-2 w-4 h-4 bg-emerald-400 rounded-full animate-bounce"></div>
                    <div className="absolute -bottom-2 -left-2 w-3 h-3 bg-cyan-400 rounded-full animate-bounce" style={{animationDelay: '0.7s'}}></div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Banner 3: Mercado Digital Conectado */}
          <section className="mb-8 md:mb-12">
            <div className="relative overflow-hidden rounded-3xl shadow-2xl h-[280px] md:h-[320px] bg-gradient-to-br from-slate-900 via-indigo-900 to-slate-800">
              {/* Patrón de fondo de red */}
              <div className="absolute inset-0 opacity-15">
                <div className="absolute inset-0" style={{
                  backgroundImage: `radial-gradient(circle at 40% 40%, rgba(99, 102, 241, 0.4) 0%, transparent 50%), 
                                   radial-gradient(circle at 60% 60%, rgba(168, 85, 247, 0.4) 0%, transparent 50%)`
                }}></div>
                {/* Patrón de red neuronal */}
                <div className="absolute inset-0 opacity-10" style={{
                  backgroundImage: `url("data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.2'%3E%3Cpath d='M50 50c0-5.523 4.477-10 10-10s10 4.477 10 10-4.477 10-10 10c0 5.523-4.477 10-10 10s-10-4.477-10-10 4.477-10 10-10zM10 10c0-5.523 4.477-10 10-10s10 4.477 10 10-4.477 10-10 10c0 5.523-4.477 10-10 10S0 25.523 0 20s4.477-10 10-10zm10 8c4.418 0 8-3.582 8-8s-3.582-8-8-8-8 3.582-8 8 3.582 8 8 8zm40 40c4.418 0 8-3.582 8-8s-3.582-8-8-8-8 3.582-8 8 3.582 8 8 8z' /%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
                  backgroundSize: '80px 80px'
                }}></div>
              </div>
              
              {/* Animación de conexiones dinámicas */}
              <div className="absolute inset-0">
                <svg className="w-full h-full opacity-20">
                  <circle cx="20%" cy="30%" r="3" fill="#6366f1">
                    <animate attributeName="r" values="3;6;3" dur="3s" repeatCount="indefinite"/>
                  </circle>
                  <circle cx="80%" cy="70%" r="3" fill="#a855f7">
                    <animate attributeName="r" values="3;6;3" dur="3s" begin="1s" repeatCount="indefinite"/>
                  </circle>
                  <circle cx="50%" cy="50%" r="4" fill="#8b5cf6">
                    <animate attributeName="r" values="4;8;4" dur="4s" repeatCount="indefinite"/>
                  </circle>
                  <line x1="20%" y1="30%" x2="50%" y2="50%" stroke="url(#connection1)" strokeWidth="2" opacity="0.6">
                    <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite"/>
                  </line>
                  <line x1="50%" y1="50%" x2="80%" y2="70%" stroke="url(#connection2)" strokeWidth="2" opacity="0.6">
                    <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" begin="1s" repeatCount="indefinite"/>
                  </line>
                  <defs>
                    <linearGradient id="connection1" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" stopColor="#6366f1" />
                      <stop offset="100%" stopColor="#8b5cf6" />
                    </linearGradient>
                    <linearGradient id="connection2" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" stopColor="#8b5cf6" />
                      <stop offset="100%" stopColor="#a855f7" />
                    </linearGradient>
                  </defs>
                </svg>
              </div>
              
              {/* Nodos de red flotantes */}
              <div className="absolute top-8 left-8 w-3 h-3 bg-indigo-400 rounded-full animate-pulse"></div>
              <div className="absolute bottom-12 right-12 w-2 h-2 bg-purple-400 rounded-full animate-ping"></div>
              <div className="absolute top-1/2 left-1/3 w-1 h-1 bg-violet-400 rounded-full animate-pulse"></div>
              
              {/* Contenido principal */}
              <div className="relative z-10 h-full flex flex-col lg:flex-row items-center justify-between p-8 md:p-12">
                {/* Lado izquierdo - Contenido */}
                <div className="flex-1 max-w-2xl text-center lg:text-left">
                  {/* Badge de mercado */}
                  <div className="inline-flex items-center gap-2 bg-gradient-to-r from-indigo-600/20 to-purple-600/20 backdrop-blur-sm px-4 py-2 rounded-full mb-6 border border-indigo-500/30">
                    <svg className="w-4 h-4 text-indigo-400" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
                    </svg>
                    <span className="text-indigo-300 font-semibold text-sm tracking-wide">MERCADO DIGITAL</span>
                  </div>
                  
                  {/* Título principal */}
                  <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6 leading-tight">
                    Conectamos Vendedores
                    <span className="block bg-gradient-to-r from-indigo-400 to-purple-400 bg-clip-text text-transparent mt-2">
                      con Clientes Reales
                    </span>
                  </h2>
                  
                  {/* Subtítulo profesional */}
                  <p className="text-gray-300 text-lg md:text-xl mb-8 leading-relaxed max-w-lg">
                    Plataforma de conexión B2B y B2C que facilita el encuentro 
                    entre oferentes y demandantes en un entorno seguro y verificado.
                  </p>
                  
                  {/* Estadísticas de mercado */}
                  <div className="flex flex-wrap gap-6 mb-8 justify-center lg:justify-start">
                    <div className="text-center">
                      <div className="text-2xl md:text-3xl font-bold text-indigo-400">100K+</div>
                      <div className="text-sm text-gray-400">Transacciones</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl md:text-3xl font-bold text-purple-400">24/7</div>
                      <div className="text-sm text-gray-400">Soporte</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl md:text-3xl font-bold text-violet-400">99.9%</div>
                      <div className="text-sm text-gray-400">Uptime</div>
                    </div>
                  </div>
                  
                  {/* Alerta de seguridad profesional */}
                  <div className="bg-amber-500/10 backdrop-blur-md rounded-xl p-4 border border-amber-500/20 mb-6">
                    <div className="flex items-start gap-3">
                      <div className="w-8 h-8 bg-amber-500/20 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg className="w-4 h-4 text-amber-400" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div>
                        <h4 className="text-amber-300 font-semibold text-sm mb-1">Transacciones Seguras</h4>
                        <p className="text-white/80 text-sm leading-relaxed">
                          Verifica perfiles, revisa productos y utiliza métodos de pago 
                          seguros. Nunca transfieras sin confirmar.
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  {/* CTA Buttons */}
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <button className="group relative bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-semibold px-8 py-4 rounded-xl transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-indigo-500/40 overflow-hidden">
                      <span className="relative z-10 text-lg">Explorar Mercado</span>
                      <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-indigo-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </button>
                    <button className="bg-white/10 hover:bg-white/20 text-white font-semibold px-8 py-4 rounded-xl transition-all duration-300 backdrop-blur-md border border-white/20 text-lg">
                      Guía de Seguridad
                    </button>
                  </div>
                </div>
                
                {/* Lado derecho - Visual de red */}
                <div className="flex-shrink-0 mt-8 lg:mt-0">
                  <div className="relative">
                    {/* Esfera principal con efecto de red */}
                    <div className="w-48 h-48 md:w-56 md:h-56 bg-gradient-to-br from-indigo-600/20 to-purple-600/20 backdrop-blur-md rounded-full flex items-center justify-center border border-indigo-500/30 relative overflow-hidden">
                      {/* Efecto de red interior */}
                      <div className="absolute inset-0 bg-gradient-to-br from-indigo-400/10 to-purple-400/10 animate-pulse"></div>
                      
                      {/* Icono central */}
                      <div className="relative z-10">
                        <svg className="w-20 h-20 md:w-24 md:h-24 text-indigo-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656-.126-1.283-.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                      </div>
                    </div>
                    
                    {/* Anillos de conexión animados */}
                    <div className="absolute inset-0 border border-indigo-400/20 rounded-full animate-ping"></div>
                    <div className="absolute inset-4 border border-purple-400/20 rounded-full animate-ping" style={{animationDelay: '1s'}}></div>
                    
                    {/* Nodos de red flotantes */}
                    <div className="absolute -top-4 -right-4 w-5 h-5 bg-indigo-400 rounded-full animate-bounce"></div>
                    <div className="absolute -bottom-4 -left-4 w-4 h-4 bg-purple-400 rounded-full animate-bounce" style={{animationDelay: '0.6s'}}></div>
                    
                    {/* Conexiones externas */}
                    <div className="absolute top-0 left-[-20px] w-3 h-3 bg-violet-400 rounded-full backdrop-blur-sm border border-violet-400/30"></div>
                    <div className="absolute bottom-8 right-[-15px] w-2 h-2 bg-indigo-400 rounded-full backdrop-blur-sm border border-indigo-400/30"></div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        
        {/* Sección Liquidaciones */}
        <CardCarousel
          title="Liquidaciones"
          subtitle="Precios increíbles en productos seleccionados"
          products={filteredProducts.slice(4, 12)}
          cardKeyPrefix="liquidaciones"
        />
                    <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                    <span className="text-purple-300 font-bold text-sm">CONECTAMOS</span>
                  </div>
                  
                  <h2 className="text-2xl md:text-3xl lg:text-4xl font-black text-white mb-4 leading-tight">
                    Vitrina Digital
                    <span className="block text-purple-300">No Ventas</span>
                  </h2>
                  
                  <p className="text-white/80 text-sm md:text-base mb-6 max-w-md leading-relaxed">
                    Conectamos vendedores con clientes. 
                    Tu seguridad es nuestra prioridad.
                  </p>
                  
                  <div className="bg-amber-500/10 backdrop-blur-md rounded-xl p-4 border border-amber-500/20">
                    <div className="flex items-start gap-3">
                      <div className="w-6 h-6 bg-amber-400 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-amber-900 text-xs font-bold">!</span>
                      </div>
                      <div>
                        <h4 className="text-amber-300 font-bold text-sm mb-1">Seguridad Primero</h4>
                        <p className="text-white/80 text-xs leading-relaxed">
                          Verifica siempre antes de transferir. 
                          Conoce al vendedor y el producto.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Lado derecho - Elementos visuales */}
                <div className="flex-shrink-0">
                  <div className="relative">
                    {/* Círculo principal */}
                    <div className="w-32 h-32 md:w-40 md:h-40 bg-gradient-to-br from-purple-400/20 to-blue-400/20 backdrop-blur-md rounded-full flex items-center justify-center border border-white/20">
                      <svg className="w-16 h-16 md:w-20 md:h-20 text-purple-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                    </div>
                    
                    {/* Elementos flotantes */}
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-pink-400 rounded-lg flex items-center justify-center shadow-lg animate-bounce" style={{animationDelay: '0.4s'}}>
                      <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                    </div>
                    <div className="absolute -bottom-2 -left-2 w-8 h-8 bg-indigo-400 rounded-lg flex items-center justify-center shadow-lg animate-bounce" style={{animationDelay: '0.8s'}}>
                      <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </InfoBannerCarousel>

  

        {/* Sección Liquidaciones */}
        <CardCarousel
          title="Liquidaciones"
          subtitle="Precios increíbles en productos seleccionados"
          products={filteredProducts.slice(4, 12)}
          cardKeyPrefix="liquidaciones"
        />
      </main>

      {/* Footer */}
      <footer className="relative text-white py-8 px-6 mt-12 shadow-2xl" style={{ background: 'linear-gradient(90deg, #3b0764 0%, #4c1d95 20%, #6d28d9 40%, var(--yellow-accent) 100%)' }}>
        <div className="container mx-auto">
          {/* Fila superior - 4 columnas */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
            
            {/* Columna 1 - Logo y descripción */}
            <div className="text-left">
              <div className="mb-4">
                <h2 className="text-2xl font-bold text-white mb-1">Solo a un</h2>
                <h2 className="text-3xl font-bold text-yellow-300">CLICK</h2>
              </div>
              <p className="text-primary-foreground/80 text-sm leading-relaxed">
                Tu guía completa de comercios, servicios y eventos.
              </p>
              <p className="text-primary-foreground/80 text-sm leading-relaxed">
                Descubre todo lo que tu ciudad tiene para ofrecer.
              </p>
            </div>

            {/* Columna 2 - Contacto */}
            <div className="text-left">
              <h3 className="text-lg font-semibold text-white mb-4 border-b border-yellow-400/30 pb-2">
                Contacto
              </h3>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Mail className="w-4 h-4 text-yellow-300" />
                  <span className="text-primary-foreground/80 text-sm"><EMAIL></span>
                </div>
                <div className="flex items-center gap-3">
                  <Phone className="w-4 h-4 text-yellow-300" />
                  <span className="text-primary-foreground/80 text-sm">****** 567 890</span>
                </div>
                <div className="flex items-center gap-3">
                  <MessageCircle className="w-4 h-4 text-yellow-300" />
                  <span className="text-primary-foreground/80 text-sm">****** 567 891</span>
                </div>
              </div>
            </div>

            {/* Columna 3 - Información */}
            <div className="text-left">
              <h3 className="text-lg font-semibold text-white mb-4 border-b border-yellow-400/30 pb-2">
                Información
              </h3>
              <div className="space-y-2">
                <a href="#" className="flex items-center gap-2 text-primary-foreground/80 text-sm hover:text-yellow-300 transition-colors duration-200">
                  <Users className="w-4 h-4 text-yellow-300" />
                  Sobre Nosotros
                </a>
                <a href="#" className="flex items-center gap-2 text-primary-foreground/80 text-sm hover:text-yellow-300 transition-colors duration-200">
                  <Store className="w-4 h-4 text-yellow-300" />
                  Registra tu Negocio
                </a>
                <a href="#" className="flex items-center gap-2 text-primary-foreground/80 text-sm hover:text-yellow-300 transition-colors duration-200">
                  <HelpCircle className="w-4 h-4 text-yellow-300" />
                  Preguntas
                </a>
              </div>
            </div>

            {/* Columna 4 - Avisos Legales */}
            <div className="text-left">
              <h3 className="text-lg font-semibold text-white mb-4 border-b border-yellow-400/30 pb-2">
                Avisos Legales
              </h3>
              <div className="space-y-2">
                <a href="#" className="flex items-center gap-2 text-primary-foreground/80 text-sm hover:text-yellow-300 transition-colors duration-200">
                  <Shield className="w-4 h-4 text-yellow-300" />
                  Privacidad
                </a>
                <a href="#" className="flex items-center gap-2 text-primary-foreground/80 text-sm hover:text-yellow-300 transition-colors duration-200">
                  <Cookie className="w-4 h-4 text-yellow-300" />
                  Cookies
                </a>
                <a href="#" className="flex items-center gap-2 text-primary-foreground/80 text-sm hover:text-yellow-300 transition-colors duration-200">
                  <RefreshCw className="w-4 h-4 text-yellow-300" />
                  Reembolso
                </a>
                <a href="#" className="flex items-center gap-2 text-primary-foreground/80 text-sm hover:text-yellow-300 transition-colors duration-200">
                  <Shield className="w-4 h-4 text-yellow-300" />
                  Seguridad
                </a>
                <a href="#" className="flex items-center gap-2 text-primary-foreground/80 text-sm hover:text-yellow-300 transition-colors duration-200">
                  <FileText className="w-4 h-4 text-yellow-300" />
                  Condiciones y términos
                </a>
              </div>
            </div>
          </div>

          {/* Fila inferior - Copyright */}
          <div className="border-t border-primary-foreground/20 pt-6">
            <div className="text-center">
              <p className="text-primary-foreground/90 text-sm mb-2">
                © 2025 Solo a un CLICK. Todos los derechos reservados.
              </p>
              <p className="text-primary-foreground/70 text-xs leading-relaxed max-w-2xl mx-auto">
                Solo a un CLICK es una plataforma de exhibición. Los productos publicados son responsabilidad exclusiva de la tienda que los ofrece.
              </p>
            </div>
          </div>
        </div>
      </footer>

      {/* Flecha de volver arriba */}
      {showScrollToTop && (
        <button
          onClick={scrollToTop}
          className="fixed right-8 top-1/2 transform -translate-y-1/2 z-50 group"
          aria-label="Volver arriba"
        >
          <div className="relative">
            {/* Círculo exterior con doble borde */}
            <div className="w-16 h-16 rounded-full border-6 border-purple-600 bg-transparent flex items-center justify-center animate-pulse shadow-2xl">
              {/* Círculo interior */}
              <div className="w-12 h-12 rounded-full border-4 border-purple-500 bg-transparent flex items-center justify-center">
                <ChevronUp className="w-8 h-8 text-purple-600 font-bold" />
              </div>
            </div>
            {/* Efecto de brillo al hover */}
            <div className="absolute inset-0 rounded-full bg-purple-400/30 opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-pulse"></div>
          </div>
        </button>
      )}
    </div>
  )
}