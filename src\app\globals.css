@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --radius: 0.625rem;
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 262.1 83.3% 57.8%;
  --primary-foreground: 210 40% 98%;
  --secondary: 220 14.3% 95.9%;
  --secondary-foreground: 222.2 47.4% 11.2%;
  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96.1%;
  --accent-foreground: 222.2 47.4% 11.2%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 262.1 83.3% 57.8%;
  --chart-1: 262.1 83.3% 57.8%;
  --chart-2: 52 100% 50%;
  --chart-3: 202 80% 40%;
  --chart-4: 40 100% 50%;
  --chart-5: 10 90% 60%;
  --sidebar: 0 0% 97%;
  --sidebar-foreground: 222.2 84% 4.9%;
  --sidebar-primary: 262.1 83.3% 57.8%;
  --sidebar-primary-foreground: 210 40% 98%;
  --sidebar-accent: 220 14.3% 95.9%;
  --sidebar-accent-foreground: 222.2 47.4% 11.2%;
  --sidebar-border: 214.3 31.8% 91.4%;
  --sidebar-ring: 262.1 83.3% 57.8%;
  
  /* Variables personalizadas para el header */
  --purple-primary: #3b0764;
  --yellow-accent: 52 80% 60%;
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 9.8%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 9.8%;
  --popover-foreground: 210 40% 98%;
  --primary: 262.1 83.3% 57.8%;
  --primary-foreground: 222.2 84% 4.9%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 262.1 83.3% 57.8%;
  --chart-1: 262.1 83.3% 57.8%;
  --chart-2: 52 80% 50%;
  --chart-3: 202 80% 40%;
  --chart-4: 40 100% 50%;
  --chart-5: 10 90% 60%;
  --sidebar: 222.2 84% 9.8%;
  --sidebar-foreground: 210 40% 98%;
  --sidebar-primary: 262.1 83.3% 57.8%;
  --sidebar-primary-foreground: 222.2 84% 4.9%;
  --sidebar-accent: 217.2 32.6% 17.5%;
  --sidebar-accent-foreground: 210 40% 98%;
  --sidebar-border: 217.2 32.6% 17.5%;
  --sidebar-ring: 262.1 83.3% 57.8%;
  
  /* Variables personalizadas para el header en modo oscuro */
  --purple-primary: #3b0764;
  --yellow-accent: 52 70% 50%;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Estilos para efectos 3D en tarjetas */
.perspective-1000 {
  perspective: 1000px;
}

.transform-style-preserve-3d {
  transform-style: preserve-3d;
}

.backface-hidden {
  backface-visibility: hidden;
}

.rotate-y-180 {
  transform: rotateY(180deg);
}

/* Estilos para touch y swipe en móviles */
.touch-pan-x {
  touch-action: pan-x;
}

.select-none {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Tamaño fijo para todas las tarjetas de productos */
.product-card-fixed-size {
  width: 200px;
  height: 320px;
  flex-shrink: 0;
}

/* Ocultar scrollbar pero mantener funcionalidad */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Mejorar la experiencia táctil en móviles */
@media (max-width: 768px) {
  .carousel-container {
    touch-action: pan-x;
    -webkit-overflow-scrolling: touch;
  }
  
  .product-card-mobile {
    min-width: calc(50% - 0.5rem);
    flex-shrink: 0;
  }
  
  /* Asegurar que las tarjetas mantengan el tamaño fijo en móvil */
  .product-card-fixed-size {
    width: 200px;
    height: 320px;
    flex-shrink: 0;
  }
}
