import { useState, useCallback, useEffect } from 'react'

interface ModalState {
  showCategorias: boolean
  showArriendos: boolean
  showServicios: boolean
  showMobileMenu: boolean
}

export const useModalManager = () => {
  const [modalState, setModalState] = useState<ModalState>({
    showCategorias: false,
    showArriendos: false,
    showServicios: false,
    showMobileMenu: false
  })

  // Función para cerrar todos los modales
  const closeAllModals = useCallback(() => {
    setModalState({
      showCategorias: false,
      showArriendos: false,
      showServicios: false,
      showMobileMenu: false
    })
  }, [])

  // Función genérica para toggle de modales
  const toggleModal = useCallback((modalKey: keyof ModalState) => {
    setModalState(prevState => {
      if (prevState[modalKey]) {
        // Si el modal actual está abierto, solo cerrarlo
        return { ...prevState, [modalKey]: false }
      } else {
        // Si el modal actual está cerrado, cerrar todos y abrir el actual
        return {
          showCategorias: false,
          showArriendos: false,
          showServicios: false,
          showMobileMenu: false,
          [modalKey]: true
        }
      }
    })
  }, [])

  // Funciones específicas para cada modal
  const toggleCategorias = useCallback(() => toggleModal('showCategorias'), [toggleModal])
  const toggleArriendos = useCallback(() => toggleModal('showArriendos'), [toggleModal])
  const toggleServicios = useCallback(() => toggleModal('showServicios'), [toggleModal])
  const toggleMobileMenu = useCallback(() => toggleModal('showMobileMenu'), [toggleModal])

  // Efecto para cerrar modales al hacer clic fuera
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const { showCategorias, showArriendos, showServicios, showMobileMenu } = modalState
      
      // Si algún modal está abierto y el clic fue fuera de los modales
      if ((showCategorias || showArriendos || showServicios || showMobileMenu) && 
          !(event.target as Element).closest('.modal-container')) {
        closeAllModals()
      }
    }

    // Solo agregar el event listener si algún modal está abierto
    const hasOpenModal = Object.values(modalState).some(Boolean)
    if (hasOpenModal) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [modalState, closeAllModals])

  return {
    ...modalState,
    closeAllModals,
    toggleCategorias,
    toggleArriendos,
    toggleServicios,
    toggleMobileMenu
  }
}