import { useState, useEffect, useMemo, useCallback } from 'react'
import { Product } from '@/types/product'

export const useProducts = () => {
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('todos')

  const fetchProducts = useCallback(async () => {
    try {
      const response = await fetch('/api/products')
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      const data = await response.json()
      setProducts(data)
    } catch (error) {
      console.error('Error fetching products:', error)
      // Set empty array on error to prevent crashes
      setProducts([])
    } finally {
      setLoading(false)
    }
  }, [])

  // Memoized filtered products to prevent unnecessary recalculations
  const filteredProducts = useMemo(() => {
    if (!Array.isArray(products)) return []
    
    return products.filter(product => {
      const matchesSearch = searchTerm === '' || 
                           product.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           product.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           product.category?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           product.source?.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesCategory = selectedCategory === 'todos' || product.category === selectedCategory
      return matchesSearch && matchesCategory
    })
  }, [products, searchTerm, selectedCategory])

  // Memoized function to get filtered products for sections
  const getFilteredProductsForSection = useCallback((startIndex: number, count: number) => {
    if (searchTerm) {
      return filteredProducts.slice(0, Math.min(count, filteredProducts.length))
    }
    return products.slice(startIndex, startIndex + count)
  }, [products, filteredProducts, searchTerm])

  // Memoized products with discounts
  const discountedProducts = useMemo(() => {
    return products.filter(p => p.discount && p.discount > 0).slice(0, 10)
  }, [products])

  useEffect(() => {
    fetchProducts()
  }, [fetchProducts])

  return {
    products,
    loading,
    searchTerm,
    selectedCategory,
    setSearchTerm,
    setSelectedCategory,
    filteredProducts,
    getFilteredProductsForSection,
    discountedProducts,
    refetchProducts: fetchProducts
  }
}