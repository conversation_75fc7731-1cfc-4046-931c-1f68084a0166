modules = ["nodejs-20", "web"]
run = "npm run dev"

[nix]
channel = "stable-24_05"

[deployment]
run = ["sh", "-c", "npm run dev"]

[[ports]]
localPort = 5000
externalPort = 80

[env]
PORT = "5000"

[workflows]
runButton = "Development Server"

[[workflows.workflow]]
name = "Development Server"
author = 36626187
mode = "sequential"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "npm run dev"
