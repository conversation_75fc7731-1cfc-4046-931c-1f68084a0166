
'use client'

import { useState, useEffect, useRef, useMemo } from 'react'
import { ProductCard } from './product-card-simple'
import { Product } from '@/types/product'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { useIsMobile } from '@/hooks/use-mobile'

interface CardCarouselProps {
  title: string
  subtitle: string
  products: Product[]
  cardKeyPrefix: string
}

interface CarouselState {
  startIndex: number
  isAutoPlaying: boolean
  direction: 'forward' | 'backward'
}

export function CardCarousel({ title, subtitle, products, cardKeyPrefix }: CardCarouselProps) {
  // Estado para una sola fila
  const [carouselState, setCarouselState] = useState<CarouselState>({
    startIndex: 0,
    isAutoPlaying: true,
    direction: 'forward'
  })

  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const restartTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // Hook para detectar móvil
  const isMobile = useIsMobile()

  // Configuración responsive - 5 productos en desktop, 2 en móvil
  const visibleCardsPerRow = isMobile ? 2 : 5
  const cardsPerMove = 2

  // Estados para touch/swipe en móvil
  const [touchStart, setTouchStart] = useState<number | null>(null)
  const [touchEnd, setTouchEnd] = useState<number | null>(null)

  const totalCards = products.length

  // Calcular el índice máximo permitido para el carrusel
  const maxIndex = useMemo(() => Math.max(0, totalCards - visibleCardsPerRow), [totalCards, visibleCardsPerRow])

  // Función para limpiar timeouts
  const clearTimeouts = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = null
    }
    if (restartTimeoutRef.current) {
      clearTimeout(restartTimeoutRef.current)
      restartTimeoutRef.current = null
    }
  }

  // Función para manejar interacción del usuario (pausar y reiniciar después)
  const handleUserInteraction = () => {
    clearTimeouts()
    setCarouselState(prev => ({ ...prev, isAutoPlaying: false }))

    // Reiniciar autoplay después de 8 segundos de inactividad
    restartTimeoutRef.current = setTimeout(() => {
      setCarouselState(prev => ({ ...prev, isAutoPlaying: true }))
    }, 8000)
  }

  // Efecto para auto-rotación del carrusel cada 5 segundos
  useEffect(() => {
    if (!carouselState.isAutoPlaying || totalCards <= visibleCardsPerRow) return

    const rotateCards = () => {
      if (carouselState.direction === 'forward') {
        if (carouselState.startIndex < maxIndex) {
          setCarouselState(prev => ({
            ...prev,
            startIndex: Math.min(prev.startIndex + cardsPerMove, maxIndex)
          }))
        } else {
          setCarouselState(prev => ({ ...prev, direction: 'backward' }))
        }
      } else {
        if (carouselState.startIndex > 0) {
          setCarouselState(prev => ({
            ...prev,
            startIndex: Math.max(prev.startIndex - cardsPerMove, 0)
          }))
        } else {
          setCarouselState(prev => ({ ...prev, direction: 'forward' }))
        }
      }
    }

    timeoutRef.current = setTimeout(() => {
      rotateCards()
    }, 5000) // Cambiado a 5 segundos según requerimientos

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
        timeoutRef.current = null
      }
    }
  }, [carouselState.isAutoPlaying, carouselState.direction, carouselState.startIndex, maxIndex, cardsPerMove, totalCards, visibleCardsPerRow])

  // Efecto para limpiar timeouts al desmontar
  useEffect(() => {
    return () => {
      clearTimeouts()
    }
  }, [])

  // Función para manejar navegación hacia adelante
  const handleNext = () => {
    const canGoNext = carouselState.startIndex < maxIndex
    if (canGoNext) {
      setCarouselState(prev => {
        const newIndex = Math.min(prev.startIndex + cardsPerMove, maxIndex)
        const newDirection = newIndex >= maxIndex ? 'backward' : 'forward'
        return {
          ...prev,
          startIndex: newIndex,
          direction: newDirection
        }
      })
      handleUserInteraction()
    }
  }

  const handlePrevious = () => {
    const canGoPrevious = carouselState.startIndex > 0
    if (canGoPrevious) {
      setCarouselState(prev => {
        const newIndex = Math.max(prev.startIndex - cardsPerMove, 0)
        const newDirection = newIndex <= 0 ? 'forward' : 'backward'
        return {
          ...prev,
          startIndex: newIndex,
          direction: newDirection
        }
      })
      handleUserInteraction()
    }
  }

  // Touch handlers para móvil
  const handleTouchStart = (e: React.TouchEvent) => {
    if (!isMobile) return
    setTouchStart(e.targetTouches[0].clientX)
    handleUserInteraction()
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isMobile) return
    setTouchEnd(e.targetTouches[0].clientX)
  }

  const handleTouchEnd = () => {
    if (!isMobile || !touchStart || !touchEnd) return
    
    const distance = touchStart - touchEnd
    const isLeftSwipe = distance > 50
    const isRightSwipe = distance < -50

    if (isLeftSwipe && carouselState.startIndex < maxIndex) {
      handleNext()
    }
    if (isRightSwipe && carouselState.startIndex > 0) {
      handlePrevious()
    }

    setTouchStart(null)
    setTouchEnd(null)
  }

  // Función para manejar interacción con las tarjetas
  const handleCardInteraction = () => {
    handleUserInteraction()
  }

  // Productos visibles en el carrusel
  const visibleProducts = products.slice(carouselState.startIndex, carouselState.startIndex + visibleCardsPerRow)

  // Determinar si mostrar flechas
  const showArrows = totalCards > visibleCardsPerRow
  const canGoPrevious = carouselState.startIndex > 0
  const canGoNext = carouselState.startIndex < maxIndex

  return (
    <section className="mb-12">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-foreground mb-2">{title}</h2>
        <p className="text-sm text-muted-foreground">{subtitle}</p>
      </div>

      {/* Carrusel con navegación */}
      <div className="relative">
        <div 
          ref={containerRef}
          className="overflow-hidden"
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
        >
          <div className={`grid ${isMobile ? 'grid-cols-2 gap-2 px-2' : 'grid-cols-5 gap-4'}`}>
            {visibleProducts.map((product) => (
              <div 
                key={`${cardKeyPrefix}-${product.id}`} 
                onClick={handleCardInteraction}
                className="product-card-fixed-size"
              >
                <ProductCard product={product} />
              </div>
            ))}
          </div>
        </div>

        {/* Flechas de navegación */}
        {showArrows && !isMobile && (
          <>
            {/* Flecha izquierda */}
            <button
              onClick={handlePrevious}
              disabled={!canGoPrevious}
              className={`absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 bg-gradient-to-r from-purple-900 via-purple-800 via-purple-700 via-purple-600 to-purple-500 border border-yellow-400 rounded-full p-2 shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-110 z-10 ${
                !canGoPrevious 
                  ? 'opacity-30 cursor-not-allowed' 
                  : 'hover:opacity-90 cursor-pointer'
              }`}
              aria-label="Ver productos anteriores"
            >
              <ChevronLeft className="h-5 w-5 text-yellow-300" />
            </button>

            {/* Flecha derecha */}
            <button
              onClick={handleNext}
              disabled={!canGoNext}
              className={`absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-4 bg-gradient-to-r from-purple-900 via-purple-800 via-purple-700 via-purple-600 to-purple-500 border border-yellow-400 rounded-full p-2 shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-110 z-10 ${
                !canGoNext 
                  ? 'opacity-30 cursor-not-allowed' 
                  : 'hover:opacity-90 cursor-pointer'
              }`}
              aria-label="Ver más productos"
            >
              <ChevronRight className="h-5 w-5 text-yellow-300" />
            </button>
          </>
        )}

        {/* Indicador de progreso (opcional) */}
        {showArrows && (
          <div className="flex justify-center mt-4 space-x-2">
            {Array.from({ length: Math.ceil(totalCards / cardsPerMove) }).map((_, index) => {
              const isActive = Math.floor(carouselState.startIndex / cardsPerMove) === index
              return (
                <div
                  key={index}
                  className={`h-2 rounded-full transition-all duration-300 ${
                    isActive 
                      ? 'w-6 bg-purple-600' 
                      : 'w-2 bg-gray-300 hover:bg-gray-400'
                  }`}
                />
              )
            })}
          </div>
        )}
      </div>
    </section>
  )
}
