
/* ========================================
   BUSCADOR FLOTANTE - NUEVA IMPLEMENTACIÓN
   ======================================== */

/* Barra de búsqueda flotante que aparece cuando el header no es visible */
.fixed-search-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 1000;
  
  /* Fondo con gradiente atractivo que coincide con el tema */
  background: linear-gradient(90deg, #3b0764 0%, #4c1d95 20%, #6d28d9 40%, #fbbf24 100%);
  
  /* Sombra elegante */
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2), 0 4px 16px rgba(107, 33, 168, 0.3);
  
  /* Padding para el contenido */
  padding: 1rem 0;
  
  /* Transición suave para aparición y desaparición */
  transform: translateY(0);
  opacity: 1;
  transition: transform 0.5s cubic-bezier(0.16, 1, 0.3, 1), 
              opacity 0.5s cubic-bezier(0.16, 1, 0.3, 1);
  
  /* Optimización de rendimiento */
  will-change: transform, opacity;
  backface-visibility: hidden;
  
  /* Backdrop blur para efecto moderno */
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

/* Clases para controlar la entrada y salida del buscador */
.fixed-search-bar.hidden {
  transform: translateY(-100%);
  opacity: 0;
  pointer-events: none;
}

.fixed-search-bar.visible {
  transform: translateY(0);
  opacity: 1;
}

/* Responsividad para móvil */
@media (max-width: 640px) {
  .fixed-search-bar {
    padding: 0.75rem 0;
  }
}

/* ========================================
   ESTILOS LEGACY (mantenidos por compatibilidad)
   ======================================== */

/* Estilos generales para el Header */
.main-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  position: relative;
  z-index: 1001;
}

.logo {
  font-weight: bold;
  font-size: 1.5rem;
}

.main-nav {
  display: flex;
  gap: 1.5rem;
}

.main-nav a {
  text-decoration: none;
  color: #333;
}

.search-bar {
  display: flex;
  gap: 0.5rem;
}

.search-bar input {
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  min-width: 300px;
}

.search-bar button {
  padding: 0.5rem 1rem;
  border: none;
  background-color: #007bff;
  color: white;
  border-radius: 4px;
  cursor: pointer;
}

/* Contenedor de búsqueda normal (sin efectos especiales) */
.search-container {
  transition: all 0.3s ease-in-out;
}

/* 
  Esta es la clase que se activa con JavaScript.
  Usamos 'position: fixed' para clavarlo en la parte superior del viewport.
*/
.search-container.fixed-search {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  
  /* Estilos para el estado fijo */
  background-color: #ffffff;
  padding: 1rem 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
  z-index: 1000; /* Debe estar por encima del contenido, pero debajo del header original si se sobreponen */

  /* Animación de entrada */
  animation: slideDown 0.3s ease-in-out;
}

/* Centrar la barra de búsqueda dentro del contenedor fijo */
.search-container.fixed-search .search-bar {
    justify-content: center;
    max-width: 800px; /* O un ancho que prefieras */
    margin: 0 auto;
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
