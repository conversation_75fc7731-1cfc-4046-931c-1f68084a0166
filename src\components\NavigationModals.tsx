'use client'

import React, { memo } from 'react'
import { ChevronDown } from 'lucide-react'

interface DropdownModalProps {
  isOpen: boolean
  onToggle: () => void
  title: string
  items: string[]
  className?: string
  iconSize?: string
}

const DropdownModal = memo(({ isOpen, onToggle, title, items, className = '', iconSize = 'w-4 h-4' }: DropdownModalProps) => (
  <div className="relative modal-container">
    <button 
      onClick={onToggle}
      className={`flex items-center space-x-2 text-white/90 hover:text-white transition-colors cursor-pointer font-medium ${className}`}
    >
      <ChevronDown className={`${iconSize} transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
      <span>{title}</span>
    </button>
    
    {isOpen && (
      <div className="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-xl border border-gray-200 z-50">
        <div className="py-2">
          {items.map((item, index) => (
            <div key={index} className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer">
              {item}
            </div>
          ))}
        </div>
      </div>
    )}
  </div>
))

DropdownModal.displayName = 'DropdownModal'

interface NavigationModalsProps {
  showCategorias: boolean
  showArriendos: boolean
  showServicios: boolean
  onToggleCategorias: () => void
  onToggleArriendos: () => void
  onToggleServicios: () => void
  isMobile?: boolean
}

const NavigationModals = memo(({
  showCategorias,
  showArriendos,
  showServicios,
  onToggleCategorias,
  onToggleArriendos,
  onToggleServicios,
  isMobile = false
}: NavigationModalsProps) => {
  const categorias = ['Electrónica', 'Ropa y Accesorios', 'Hogar y Jardín', 'Deportes', 'Belleza y Salud']
  const arriendos = ['Departamentos', 'Casas', 'Oficinas', 'Bodegas', 'Estacionamientos']
  const servicios = ['Servicios Profesionales', 'Mantenimiento y Reparación', 'Consultoría', 'Educación y Cursos', 'Salud y Bienestar']
  
  const mobileClasses = isMobile 
    ? 'text-[10px] space-x-1'
    : 'text-sm space-x-2'
  const mobileIconSize = isMobile ? 'w-3 h-3' : 'w-4 h-4'

  return (
    <>
      <DropdownModal
        isOpen={showCategorias}
        onToggle={onToggleCategorias}
        title="Categorías"
        items={categorias}
        className={mobileClasses}
        iconSize={mobileIconSize}
      />
      
      <DropdownModal
        isOpen={showArriendos}
        onToggle={onToggleArriendos}
        title="Arriendos"
        items={arriendos}
        className={mobileClasses}
        iconSize={mobileIconSize}
      />
      
      <DropdownModal
        isOpen={showServicios}
        onToggle={onToggleServicios}
        title="Servicios"
        items={servicios}
        className={mobileClasses}
        iconSize={mobileIconSize}
      />
    </>
  )
})

NavigationModals.displayName = 'NavigationModals'

export default NavigationModals