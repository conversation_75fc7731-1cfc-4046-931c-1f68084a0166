// Test script to verify server is working
const http = require('http');

const options = {
  hostname: 'localhost',
  port: 3000,
  path: '/',
  method: 'GET'
};

console.log('🔍 Testing server connection to http://localhost:3000...');

const req = http.request(options, (res) => {
  console.log(`✅ Server is responding!`);
  console.log(`Status Code: ${res.statusCode}`);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log(`✅ Response received successfully`);
    console.log(`Content length: ${data.length} characters`);
    
    if (data.includes('Villarrica Click')) {
      console.log(`✅ Page content is correct - contains "Villarrica Click"`);
      console.log(`🎉 SERVER IS WORKING PERFECTLY!`);
    } else {
      console.log(`❌ Page content doesn't contain expected text`);
      console.log(`First 200 characters:`, data.substring(0, 200));
    }
    process.exit(0);
  });
});

req.on('error', (e) => {
  console.error(`❌ Server connection failed: ${e.message}`);
  process.exit(1);
});

req.setTimeout(5000, () => {
  console.error(`❌ Server request timeout`);
  req.destroy();
  process.exit(1);
});

req.end();
