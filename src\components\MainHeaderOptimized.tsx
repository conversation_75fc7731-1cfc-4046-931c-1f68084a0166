'use client'

import React, { memo } from 'react'
import { SearchBar } from '@/components/search-bar'

interface MainHeaderOptimizedProps {
  headerRef: React.RefObject<HTMLElement>
  searchTerm: string
  onSearchChange: (value: string) => void
  isSearchFixed: boolean
}

const MainHeaderOptimized = memo(({
  headerRef,
  searchTerm,
  onSearchChange,
  isSearchFixed
}: MainHeaderOptimizedProps) => (
  <header 
    ref={headerRef} 
    className="relative text-white shadow-2xl" 
    style={{ background: 'linear-gradient(90deg, #3b0764 0%, #4c1d95 20%, #6d28d9 40%, var(--yellow-accent) 100%)' }}
  >
    <div className="container mx-auto relative z-10">
      {/* Versión Desktop */}
      <div className="hidden sm:block py-6 px-6">
        <div className="flex items-center justify-between">
          {/* Logo/Título - Dos filas a la izquierda */}
          <div className="flex flex-col items-start">
            <h1 className="text-xl font-bold leading-tight text-white">Solo a</h1>
            <h1 className="text-4xl font-bold leading-tight text-yellow-300">un CLICK</h1>
          </div>

          {/* Buscador en el centro */}
          <div className={`search-container flex-1 max-w-lg mx-8 ${isSearchFixed ? 'fixed-search' : ''}`}>
            <SearchBar
              value={searchTerm}
              onChange={onSearchChange}
              placeholder="Buscar productos..."
            />
          </div>

          {/* Espacio publicitario a la derecha */}
          <div className="group w-56 h-16 bg-gradient-to-br from-purple-700/80 via-purple-600/70 to-purple-500/60 border-2 border-purple-300/60 rounded-xl flex items-center justify-center p-3 hover:shadow-xl hover:shadow-purple-400/40 transition-all duration-500 hover:scale-105 backdrop-blur-sm relative overflow-hidden">
            {/* Efecto de brillo */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent transform -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
            
            {/* Icono decorativo */}
            <div className="absolute top-1 right-2">
              <svg className="w-3 h-3 text-white/60" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8 2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
              </svg>
            </div>
            
            <div className="text-center relative z-10">
              <div className="text-[9px] font-extrabold text-white leading-tight tracking-wider uppercase">Espacio Premium</div>
              <div className="text-xs font-bold text-white leading-tight tracking-wide">Publicitario</div>
              <div className="text-[9px] text-purple-200 leading-tight mt-0.5">• Disponible •</div>
            </div>
          </div>
        </div>
      </div>

      {/* Versión Móvil */}
      <div className="sm:hidden w-full">
        {/* Primera fila: Título y buscador */}
        <div className="flex items-center justify-between px-4 py-3">
          {/* Logo/Título - Más pequeño y a la izquierda */}
          <div className="flex flex-col items-start">
            <h1 className="text-sm font-bold leading-tight text-white">Solo a</h1>
            <h1 className="text-lg font-bold leading-tight text-yellow-300">un CLICK</h1>
          </div>

          {/* Buscador a la derecha - Más pequeño */}
          <div className={`search-container flex-1 max-w-[180px] ml-3 ${isSearchFixed ? 'fixed-search' : ''}`}>
            <SearchBar
              value={searchTerm}
              onChange={onSearchChange}
              placeholder="Buscar..."
              className="text-sm"
            />
          </div>
        </div>

        {/* Segunda fila: Banner publicitario */}
        <div className="px-4 pb-3">
          <div className="group w-full h-12 bg-gradient-to-br from-purple-700/80 via-purple-600/70 to-purple-500/60 border-2 border-purple-300/60 rounded-lg flex items-center justify-center p-2 hover:shadow-lg hover:shadow-purple-400/40 transition-all duration-500 backdrop-blur-sm relative overflow-hidden">
            {/* Efecto de brillo */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent transform -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
            
            {/* Icono decorativo */}
            <div className="absolute top-1 right-2">
              <svg className="w-2 h-2 text-white/60" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8 2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
              </svg>
            </div>
            
            <div className="text-center relative z-10">
              <div className="text-[8px] font-extrabold text-white leading-tight tracking-wider uppercase">Espacio Premium</div>
              <div className="text-[10px] font-bold text-white leading-tight tracking-wide">Publicitario</div>
              <div className="text-[8px] text-purple-200 leading-tight mt-0.5">• Disponible •</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>
))

MainHeaderOptimized.displayName = 'MainHeaderOptimized'

export default MainHeaderOptimized