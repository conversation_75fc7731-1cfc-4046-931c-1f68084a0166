import { useState, useEffect } from 'react';

/**
 * Hook personalizado para obtener la posición del scroll.
 * @returns La posición vertical (Y) del scroll.
 */
export const useScroll = () => {
  const [scrollY, setScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    // Añadir el listener solo en el lado del cliente
    if (typeof window !== 'undefined') {
      window.addEventListener('scroll', handleScroll);

      // Limpiar el listener al desmontar el componente
      return () => {
        window.removeEventListener('scroll', handleScroll);
      };
    }
  }, []); // El array vacío asegura que el efecto se ejecute solo una vez

  return scrollY;
};