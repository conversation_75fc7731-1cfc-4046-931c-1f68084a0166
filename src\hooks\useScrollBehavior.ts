import { useState, useEffect, useRef, useCallback } from 'react'

export const useScrollBehavior = () => {
  const [showScrollToTop, setShowScrollToTop] = useState(false)
  const [isSearchFixed, setIsSearchFixed] = useState(false)
  const headerRef = useRef<HTMLElement | null>(null)
  
  // Throttle scroll events for better performance
  const throttleRef = useRef<NodeJS.Timeout | null>(null)

  const handleScroll = useCallback(() => {
    if (headerRef.current) {
      const headerRect = headerRef.current.getBoundingClientRect()
      
      // El buscador aparece cuando el header NO es visible (está completamente fuera de la vista)
      const shouldFixSearch = headerRect.bottom <= 0
      
      // Batch state updates to prevent multiple renders
      setIsSearchFixed(prev => prev !== shouldFixSearch ? shouldFixSearch : prev)
      setShowScrollToTop(prev => {
        const shouldShow = headerRect.bottom < 0
        return prev !== shouldShow ? shouldShow : prev
      })
    }
  }, [])

  // Throttled scroll handler
  const throttledScrollHandler = useCallback(() => {
    if (throttleRef.current) {
      clearTimeout(throttleRef.current)
    }
    
    throttleRef.current = setTimeout(handleScroll, 10) // 10ms throttle
  }, [handleScroll])

  useEffect(() => {
    // Agregar event listener de scroll con throttling
    window.addEventListener('scroll', throttledScrollHandler, { passive: true })
    
    // Verificar estado inicial
    handleScroll()
    
    // Limpiar event listener
    return () => {
      window.removeEventListener('scroll', throttledScrollHandler)
      if (throttleRef.current) {
        clearTimeout(throttleRef.current)
      }
    }
  }, [throttledScrollHandler, handleScroll])

  return {
    showScrollToTop,
    isSearchFixed,
    headerRef
  }
}