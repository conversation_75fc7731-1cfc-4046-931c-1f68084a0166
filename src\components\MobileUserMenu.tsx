'use client'

import React, { memo } from 'react'
import { Menu } from 'lucide-react'

interface MobileUserMenuProps {
  showMobileMenu: boolean
  onToggleMobileMenu: () => void
}

const MobileUserMenu = memo(({ showMobileMenu, onToggleMobileMenu }: MobileUserMenuProps) => (
  <div className="relative modal-container">
    <button
      onClick={onToggleMobileMenu}
      className="flex items-center justify-center w-8 h-8 text-white/90 hover:text-white transition-colors cursor-pointer"
      aria-label="Toggle menu"
    >
      <Menu className="w-5 h-5" />
    </button>
    
    {showMobileMenu && (
      <div className="absolute top-full right-0 mt-1 w-40 bg-white rounded-lg shadow-xl border border-gray-200 z-50">
        <div className="py-1">
          <div className="flex items-center space-x-2 px-3 py-2 text-xs text-gray-700 hover:bg-gray-100 cursor-pointer">
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
            </svg>
            <span>Registrarse</span>
          </div>
          <div className="flex items-center space-x-2 px-3 py-2 text-xs text-gray-700 hover:bg-gray-100 cursor-pointer">
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
            </svg>
            <span>Ingresar</span>
          </div>
        </div>
      </div>
    )}
  </div>
))

MobileUserMenu.displayName = 'MobileUserMenu'

export default MobileUserMenu