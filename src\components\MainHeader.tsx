"use client";

import React, { useState, useEffect, useRef } from 'react';
import './MainHeader.css'; // Importaremos los estilos que crearemos a continuación

const MainHeader = () => {
  const [isSearchFixed, setIsSearchFixed] = useState(false);
  const headerRef = useRef<HTMLElement | null>(null);

  useEffect(() => {
    const handleScroll = () => {
      if (headerRef.current) {
        // La altura del header. El buscador se fijará cuando el scroll supere esta altura.
        const headerBottom = headerRef.current.getBoundingClientRect().bottom;
        
        // Si la parte inferior del header está fuera de la vista (o a punto de estarlo)
        if (window.scrollY > headerRef.current.offsetHeight) {
          setIsSearchFixed(true);
        } else {
          setIsSearchFixed(false);
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    
    // Limpieza del evento
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []); // El array vacío asegura que esto se configure solo una vez

  return (
    <>
      <header className="main-header" ref={headerRef}>
        <div className="logo">MiLogo</div>
        
        {/* El contenedor del buscador es el que cambiará */}
        <div 
          id="search-container" 
          className={`search-container ${isSearchFixed ? 'fixed-search' : ''}`}
        >
          <div className="search-bar">
            <input type="text" placeholder="Buscar productos..." />
            <button>Buscar</button>
          </div>
        </div>
        
        <nav className="main-nav">
          <a href="#">Inicio</a>
          <a href="#">Categorías</a>
          <a href="#">Ofertas</a>
        </nav>
      </header>
      {/* 
        Este es un marcador de posición que ocupa la altura del buscador 
        cuando este se fija. Esto previene el salto de contenido. 
        Se muestra solo cuando el buscador está fijo.
      */}
      {isSearchFixed && <div style={{ height: '78px' }}></div>}
    </>
  );
};

export default MainHeader;